import { initializeApp } from 'firebase/app';
import { getA<PERSON>, GoogleAuthProvider, connectAuthEmulator } from 'firebase/auth';
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore';
import { browser } from '$app/environment';
import { logger } from '$lib/utils/logger';

// Define the CORS proxy URL for development
const CORS_PROXY_URL = import.meta.env.VITE_CORS_PROXY_URL || 'http://localhost:3001/auth';
const USE_CORS_PROXY = import.meta.env.VITE_USE_CORS_PROXY === 'true';

// Firebase configuration
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID,
  measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);

// Note: CORS proxy configuration is handled in cors-proxy.js
// The proxy server should be running on localhost:3001
if (browser && USE_CORS_PROXY) {
  logger.info('CORS proxy mode enabled', { proxyUrl: CORS_PROXY_URL });
}

const db = getFirestore(app);
const googleProvider = new GoogleAuthProvider();

// Configure Google Auth Provider
googleProvider.setCustomParameters({
  prompt: 'select_account'
});

// Use emulators if enabled
if (browser && import.meta.env.VITE_USE_FIREBASE_EMULATOR === 'true') {
  try {
    connectAuthEmulator(auth, 'http://localhost:9099');
    connectFirestoreEmulator(db, 'localhost', 8080);
    logger.info('Firebase emulators connected');
  } catch (error) {
    logger.error('Failed to connect to Firebase emulators', error);
  }
}

export { app, auth, db, googleProvider };
